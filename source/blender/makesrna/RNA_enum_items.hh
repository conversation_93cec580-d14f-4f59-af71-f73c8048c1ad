/* SPDX-FileCopyrightText: 2023 Blender Authors
 *
 * SPDX-License-Identifier: GPL-2.0-or-later */

/** \file
 * \ingroup RNA
 *
 * Notes:
 * - This is included multiple times with different #defines for DEF_ENUM.
 * - As these are global identifiers, all must confirm to `rna_enum_*_items` convention.
 */

/* Use in cases where only dynamic types are used. */
DEF_ENUM(rna_enum_dummy_NULL_items)
DEF_ENUM(rna_enum_dummy_DEFAULT_items)

DEF_ENUM(rna_enum_id_type_items)

DEF_ENUM(rna_enum_object_mode_items)
DEF_ENUM(rna_enum_workspace_object_mode_items)
DEF_ENUM(rna_enum_object_empty_drawtype_items)
DEF_ENUM(rna_enum_object_gpencil_type_items)
DEF_ENUM(rna_enum_metaelem_type_items)

DEF_ENUM(rna_enum_color_space_convert_default_items)
DEF_ENUM(rna_enum_proportional_falloff_items)
DEF_ENUM(rna_enum_proportional_falloff_curve_only_items)
DEF_ENUM(rna_enum_snap_source_items)
DEF_ENUM(rna_enum_snap_element_items)
DEF_ENUM(rna_enum_snap_animation_element_items)
DEF_ENUM(rna_enum_curve_fit_method_items)
DEF_ENUM(rna_enum_mesh_select_mode_items)
DEF_ENUM(rna_enum_mesh_select_mode_uv_items)
DEF_ENUM(rna_enum_mesh_delimit_mode_items)
DEF_ENUM(rna_enum_space_graph_mode_items)
DEF_ENUM(rna_enum_space_file_browse_mode_items)
DEF_ENUM(rna_enum_space_sequencer_view_type_items)
DEF_ENUM(rna_enum_space_type_items)
DEF_ENUM(rna_enum_space_image_mode_items)
DEF_ENUM(rna_enum_space_image_mode_all_items)
DEF_ENUM(rna_enum_space_action_mode_items)
DEF_ENUM(rna_enum_fileselect_params_sort_items)
DEF_ENUM(rna_enum_region_type_items)
DEF_ENUM(rna_enum_object_modifier_type_items)
DEF_ENUM(rna_enum_constraint_type_items)
DEF_ENUM(rna_enum_boidrule_type_items)
DEF_ENUM(rna_enum_strip_modifier_type_items)
DEF_ENUM(rna_enum_strip_video_modifier_type_items)
DEF_ENUM(rna_enum_strip_sound_modifier_type_items)
DEF_ENUM(rna_enum_strip_scale_method_items)
DEF_ENUM(rna_enum_object_shaderfx_type_items)

DEF_ENUM(rna_enum_modifier_triangulate_quad_method_items)
DEF_ENUM(rna_enum_modifier_triangulate_ngon_method_items)
DEF_ENUM(rna_enum_modifier_shrinkwrap_mode_items)
DEF_ENUM(rna_enum_shrinkwrap_type_items)
DEF_ENUM(rna_enum_shrinkwrap_face_cull_items)
DEF_ENUM(rna_enum_node_warning_type_items)

DEF_ENUM(rna_enum_image_type_all_items)
DEF_ENUM(rna_enum_image_color_mode_items)
DEF_ENUM(rna_enum_image_color_depth_items)
DEF_ENUM(rna_enum_image_generated_type_items)

DEF_ENUM(rna_enum_normal_space_items)
DEF_ENUM(rna_enum_normal_swizzle_items)
DEF_ENUM(rna_enum_bake_save_mode_items)
DEF_ENUM(rna_enum_bake_margin_type_items)
DEF_ENUM(rna_enum_bake_target_items)

DEF_ENUM(rna_enum_views_format_items)
DEF_ENUM(rna_enum_views_format_multilayer_items)
DEF_ENUM(rna_enum_views_format_multiview_items)
DEF_ENUM(rna_enum_stereo3d_display_items)
DEF_ENUM(rna_enum_stereo3d_anaglyph_type_items)
DEF_ENUM(rna_enum_stereo3d_interlace_type_items)

#ifdef WITH_IMAGE_OPENEXR
DEF_ENUM(rna_enum_exr_codec_items)
#endif
DEF_ENUM(rna_enum_color_sets_items)

DEF_ENUM(rna_enum_beztriple_keyframe_type_items)
DEF_ENUM(rna_enum_beztriple_interpolation_mode_items)
DEF_ENUM(rna_enum_beztriple_interpolation_easing_items)
DEF_ENUM(rna_enum_fcurve_auto_smoothing_items)
DEF_ENUM(rna_enum_keyframe_handle_type_items)
DEF_ENUM(rna_enum_driver_target_rotation_mode_items)

DEF_ENUM(rna_enum_keyingset_path_grouping_items)
DEF_ENUM(rna_enum_keying_flag_items)
DEF_ENUM(rna_enum_keying_flag_api_items)

DEF_ENUM(rna_enum_fmodifier_type_items)

DEF_ENUM(rna_enum_motionpath_bake_location_items)
DEF_ENUM(rna_enum_motionpath_display_type_items)
DEF_ENUM(rna_enum_motionpath_range_items)

DEF_ENUM(rna_enum_event_value_items)
DEF_ENUM(rna_enum_event_direction_items)

DEF_ENUM(rna_enum_event_type_items)
DEF_ENUM(rna_enum_event_type_mask_items)

DEF_ENUM(rna_enum_operator_type_flag_items)
DEF_ENUM(rna_enum_operator_return_items)
DEF_ENUM(rna_enum_operator_property_tag_items)

DEF_ENUM(rna_enum_brush_automasking_flag_items)
DEF_ENUM(rna_enum_brush_sculpt_brush_type_items)
DEF_ENUM(rna_enum_brush_vertex_brush_type_items)
DEF_ENUM(rna_enum_brush_weight_brush_type_items)
DEF_ENUM(rna_enum_brush_gpencil_types_items)
DEF_ENUM(rna_enum_brush_gpencil_vertex_types_items)
DEF_ENUM(rna_enum_brush_gpencil_sculpt_types_items)
DEF_ENUM(rna_enum_brush_gpencil_weight_types_items)
DEF_ENUM(rna_enum_brush_curves_sculpt_brush_type_items)
DEF_ENUM(rna_enum_brush_image_brush_type_items)
DEF_ENUM(rna_enum_brush_curve_preset_items)

DEF_ENUM(rna_enum_grease_pencil_selectmode_items)
DEF_ENUM(rna_enum_stroke_depth_order_items)

DEF_ENUM(rna_enum_axis_xy_items)
DEF_ENUM(rna_enum_axis_xyz_items)

DEF_ENUM(rna_enum_axis_flag_xyz_items)

DEF_ENUM(rna_enum_symmetrize_direction_items)

DEF_ENUM(rna_enum_texture_type_items)

DEF_ENUM(rna_enum_light_type_items)

DEF_ENUM(rna_enum_lightprobes_type_items)

DEF_ENUM(rna_enum_unpack_method_items)

DEF_ENUM(rna_enum_object_type_items)
DEF_ENUM(rna_enum_object_rotation_mode_items)

DEF_ENUM(rna_enum_object_type_curve_items)

DEF_ENUM(rna_enum_rigidbody_object_type_items)
DEF_ENUM(rna_enum_rigidbody_object_shape_items)
DEF_ENUM(rna_enum_rigidbody_constraint_type_items)

DEF_ENUM(rna_enum_object_axis_items)

DEF_ENUM(rna_enum_render_pass_type_items)

DEF_ENUM(rna_enum_bake_pass_type_items)
DEF_ENUM(rna_enum_bake_pass_filter_type_items)

DEF_ENUM(rna_enum_keymap_propvalue_items)

DEF_ENUM(rna_enum_operator_context_items)

DEF_ENUM(rna_enum_wm_report_items)
DEF_ENUM(rna_enum_wm_job_type_items)

DEF_ENUM(rna_enum_property_type_items)
DEF_ENUM(rna_enum_property_subtype_items)
DEF_ENUM(rna_enum_property_subtype_string_items)
DEF_ENUM(rna_enum_property_subtype_number_items)
DEF_ENUM(rna_enum_property_subtype_number_array_items)
DEF_ENUM(rna_enum_property_unit_items)
DEF_ENUM(rna_enum_property_flag_items)
DEF_ENUM(rna_enum_property_flag_enum_items)
DEF_ENUM(rna_enum_property_override_flag_items)
DEF_ENUM(rna_enum_property_override_flag_collection_items)
DEF_ENUM(rna_enum_property_string_search_flag_items)

DEF_ENUM(rna_enum_shading_type_items)

DEF_ENUM(rna_enum_navigation_mode_items)

DEF_ENUM(rna_enum_node_socket_in_out_items)
DEF_ENUM(rna_enum_node_socket_type_items)

DEF_ENUM(rna_enum_node_tree_interface_item_type_items)
DEF_ENUM(rna_enum_node_socket_structure_type_items)

DEF_ENUM(rna_enum_node_math_items)
DEF_ENUM(rna_enum_mapping_type_items)
DEF_ENUM(rna_enum_node_vec_math_items)
DEF_ENUM(rna_enum_node_boolean_math_items)
DEF_ENUM(rna_enum_node_float_compare_items)
DEF_ENUM(rna_enum_node_compare_operation_items)
DEF_ENUM(rna_enum_node_integer_math_items)
DEF_ENUM(rna_enum_node_float_to_int_items)
DEF_ENUM(rna_enum_node_map_range_items)
DEF_ENUM(rna_enum_node_clamp_items)

DEF_ENUM(rna_enum_node_compositor_extension_items)
DEF_ENUM(rna_enum_node_compositor_interpolation_items)

DEF_ENUM(rna_enum_ramp_blend_items)

DEF_ENUM(rna_enum_prop_dynamicpaint_type_items)

DEF_ENUM(rna_enum_clip_editor_mode_items)

DEF_ENUM(rna_enum_icon_items)
DEF_ENUM(rna_enum_uilist_layout_type_items)

DEF_ENUM(rna_enum_linestyle_color_modifier_type_items)
DEF_ENUM(rna_enum_linestyle_alpha_modifier_type_items)
DEF_ENUM(rna_enum_linestyle_thickness_modifier_type_items)
DEF_ENUM(rna_enum_linestyle_geometry_modifier_type_items)

DEF_ENUM(rna_enum_window_cursor_items)

DEF_ENUM(rna_enum_dt_method_vertex_items)
DEF_ENUM(rna_enum_dt_method_edge_items)
DEF_ENUM(rna_enum_dt_method_loop_items)
DEF_ENUM(rna_enum_dt_method_poly_items)
DEF_ENUM(rna_enum_dt_mix_mode_items)
DEF_ENUM(rna_enum_dt_layers_select_src_items)
DEF_ENUM(rna_enum_dt_layers_select_dst_items)

DEF_ENUM(rna_enum_context_mode_items)

DEF_ENUM(rna_enum_preference_section_items)

DEF_ENUM(rna_enum_attribute_type_items)
DEF_ENUM(rna_enum_color_attribute_type_items)
DEF_ENUM(rna_enum_attribute_type_with_auto_items)
DEF_ENUM(rna_enum_attribute_domain_items)
DEF_ENUM(rna_enum_attribute_domain_edge_face_items)
DEF_ENUM(rna_enum_attribute_domain_only_mesh_items)
DEF_ENUM(rna_enum_attribute_domain_only_mesh_no_edge_items)
DEF_ENUM(rna_enum_attribute_domain_only_mesh_no_corner_items)
DEF_ENUM(rna_enum_attribute_domain_point_face_curve_items)
DEF_ENUM(rna_enum_attribute_domain_point_edge_face_curve_items)
DEF_ENUM(rna_enum_attribute_curves_domain_items)
DEF_ENUM(rna_enum_color_attribute_domain_items)
DEF_ENUM(rna_enum_attribute_domain_without_corner_items)
DEF_ENUM(rna_enum_attribute_domain_with_auto_items)
DEF_ENUM(rna_enum_geometry_component_type_items)
DEF_ENUM(rna_enum_node_combsep_color_items)
DEF_ENUM(rna_enum_node_socket_data_type_items)

DEF_ENUM(rna_enum_node_geometry_curve_handle_side_items)
DEF_ENUM(rna_enum_node_geometry_mesh_circle_fill_type_items)

DEF_ENUM(rna_enum_volume_grid_data_type_items)

DEF_ENUM(rna_enum_collection_color_items)
DEF_ENUM(rna_enum_strip_color_items)

DEF_ENUM(rna_enum_subdivision_uv_smooth_items)
DEF_ENUM(rna_enum_subdivision_boundary_smooth_items)

DEF_ENUM(rna_enum_transform_orientation_items)

DEF_ENUM(rna_enum_velocity_unit_items)

DEF_ENUM(rna_enum_curves_type_items)
DEF_ENUM(rna_enum_curves_handle_type_items)
DEF_ENUM(rna_enum_curve_normal_mode_items)

DEF_ENUM(rna_enum_geometry_nodes_gizmo_color_items)
DEF_ENUM(rna_enum_geometry_nodes_linear_gizmo_draw_style_items)

/* Not available to RNA pre-processing (`makesrna`).
 * Defined in editors for example. */
#ifndef RNA_MAKESRNA

DEF_ENUM(rna_enum_particle_edit_hair_brush_items)
DEF_ENUM(rna_enum_particle_edit_disconnected_hair_brush_items)

DEF_ENUM(rna_enum_keyframe_paste_offset_items)
DEF_ENUM(rna_enum_keyframe_paste_offset_value_items)
DEF_ENUM(rna_enum_keyframe_paste_merge_items)

DEF_ENUM(rna_enum_transform_pivot_full_items)
DEF_ENUM(rna_enum_transform_mode_type_items)

/* In the runtime part of RNA, could be removed from this section. */
DEF_ENUM(rna_enum_nla_mode_extend_items)
DEF_ENUM(rna_enum_nla_mode_blend_items)
DEF_ENUM(rna_enum_keyblock_type_items)

DEF_ENUM(rna_enum_asset_library_type_items)

#endif

#undef DEF_ENUM
