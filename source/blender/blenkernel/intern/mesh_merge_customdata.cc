/* SPDX-FileCopyrightText: 2023 Blender Authors
 *
 * SPDX-License-Identifier: GPL-2.0-or-later */

/** \file
 * \ingroup bke
 */

#include "BLI_math_base.h"
#include "BLI_math_vector_types.hh"
#include "BLI_task.hh"

#include "BKE_attribute.hh"
#include "BKE_mesh.hh"

#include "BLI_strict_flags.h" /* IWYU pragma: keep. Keep last. */

using namespace blender;

enum {
  CMP_CLOSE = 0,
  CMP_EQUAL = 1,
  CMP_APART = 2,
};

static int compare_v2_classify(const float uv_a[2], const float uv_b[2])
{
  if (uv_a[0] == uv_b[0] && uv_a[1] == uv_b[1]) {
    return CMP_EQUAL;
  }
  /* NOTE(@ideasman42): that the ULP value is the primary value used to compare relative
   * values as the absolute value doesn't account for float precision at difference scales.
   * - For subdivision-surface ULP of 3 is sufficient,
   *   although this value is extremely small.
   * - For bevel the ULP of 12 is sufficient to merge UVs that appear to be connected
   *   with bevel on Suzanne beveled 15% with 6 segments.
   *
   * These values could be tweaked but should be kept on the small side to prevent
   * unintentional joining of intentionally disconnected UVs.
   *
   * Before v2.91 the threshold was either (`1e-4` or `0.05 / image_size` for selection picking).
   * So picking used a threshold of `1e-4` for a 500x500 image and `1e-5` for a 5000x5000 image.
   * Given this value worked reasonably well for a long time, the absolute difference should
   * never exceed `1e-4` (#STD_UV_CONNECT_LIMIT which is still used in a few areas). */
  const float diff_abs = 1e-12f;
  const int diff_ulp = 12;

  if (compare_ff_relative(uv_a[0], uv_b[0], diff_abs, diff_ulp) &&
      compare_ff_relative(uv_a[1], uv_b[1], diff_abs, diff_ulp))
  {
    return CMP_CLOSE;
  }
  return CMP_APART;
}

static void merge_uvs_for_vertex(const Span<int> loops_for_vert, Span<float2 *> uv_map_layers)
{
  if (loops_for_vert.size() <= 1) {
    return;
  }
  /* Manipulate a copy of the loop indices, de-duplicating UVs per layer. */
  Vector<int, 32> loops_merge;
  loops_merge.reserve(loops_for_vert.size());
  for (float2 *uv_map : uv_map_layers) {
    BLI_assert(loops_merge.is_empty());
    loops_merge.extend_unchecked(loops_for_vert);
    while (loops_merge.size() > 1) {
      uint i_last = uint(loops_merge.size()) - 1;
      const float *uv_src = uv_map[loops_merge[0]];
      for (uint i = 1; i <= i_last;) {
        float *uv_dst = uv_map[loops_merge[i]];
        switch (compare_v2_classify(uv_src, uv_dst)) {
          case CMP_CLOSE: {
            uv_dst[0] = uv_src[0];
            uv_dst[1] = uv_src[1];
            ATTR_FALLTHROUGH;
          }
          case CMP_EQUAL: {
            loops_merge[i] = loops_merge[i_last--];
            break;
          }
          case CMP_APART: {
            /* Doesn't match, check the next UV. */
            i++;
            break;
          }
          default: {
            BLI_assert_unreachable();
          }
        }
      }
      /* Finished de-duplicating with the first index, throw it away. */
      loops_merge[0] = loops_merge[i_last];
      loops_merge.resize(i_last);
    }
    loops_merge.clear();
  }
}

void BKE_mesh_merge_customdata_for_apply_modifier(Mesh *mesh)
{
  using namespace blender::bke;
  if (mesh->corners_num == 0) {
    return;
  }
  MutableAttributeAccessor attributes = mesh->attributes_for_write();
  Vector<SpanAttributeWriter<float2>> uv_map_attrs;
  attributes.foreach_attribute([&](const bke::AttributeIter &iter) {
    if (iter.data_type != AttrType::Float2) {
      return;
    }
    if (iter.domain != AttrDomain::Corner) {
      return;
    }
    uv_map_attrs.append(attributes.lookup_for_write_span<float2>(iter.name));
  });

  if (uv_map_attrs.is_empty()) {
    return;
  }

  const GroupedSpan<int> vert_to_corner = mesh->vert_to_corner_map();

  Vector<float2 *> uv_map_layers;
  for (SpanAttributeWriter<float2> &attr : uv_map_attrs) {
    uv_map_layers.append(attr.span.data());
  }

  threading::parallel_for(IndexRange(mesh->verts_num), 1024, [&](IndexRange range) {
    for (const int64_t v_index : range) {
      merge_uvs_for_vertex(vert_to_corner[v_index], uv_map_layers);
    }
  });

  for (SpanAttributeWriter<float2> &attr : uv_map_attrs) {
    attr.finish();
  }
}
