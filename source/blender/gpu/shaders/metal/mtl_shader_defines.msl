/* SPDX-FileCopyrightText: 2022 Blender Authors
 *
 * SPDX-License-Identifier: GPL-2.0-or-later */

/** Special header for mapping commonly defined tokens to API-specific variations.
 * Where possible, this will adhere closely to base GLSL, where semantics are the same.
 * However, host code shader code may need modifying to support types where necessary variations
 * exist between APIs but are not expressed through the source. (e.g. distinction between depth2d
 * and texture2d types in metal).
 */

/* Suppress unhelpful shader compiler warnings. */
#pragma clang diagnostic ignored "-Wunused-variable"
#pragma clang diagnostic ignored "-Wcomment"

#define ENABLE_IF(cond) thread metal::enable_if_t<(cond)> * = nullptr

/* Base instance with offsets. */
#define gpu_BaseInstance gl_BaseInstanceARB
#define gpu_InstanceIndex (gl_InstanceID + gpu_BaseInstance)

#ifdef MTL_WORKGROUP_SIZE_X
/* Older Metal compiler version don't treat vector component access as constexpr.
 * We have to make a wrapper class for that otherwise we cannot use WorkGroupSize for sizing
 * threadgroup arrays. Note that this bug is not present in the version 4.1 of the compiler. */
struct mtl_WorkGroupSize {
  union {
    struct {
      uint x, y, z;
    };
    uint2 xy;
    uint3 xyz;
  };

  constexpr mtl_WorkGroupSize()
      : x(MTL_WORKGROUP_SIZE_X), y(MTL_WORKGROUP_SIZE_Y), z(MTL_WORKGROUP_SIZE_Z)
  {
  }

  constexpr inline operator uint3() const
  {
    return xyz;
  }
};
#  define gl_WorkGroupSize mtl_WorkGroupSize()
#endif

/* Type definitions. */
/* int implicitly cast to bool in MSL. */
using bool32_t = int32_t;
using vec3_1010102_Unorm = uint32_t;
using vec3_1010102_Inorm = int32_t;
/* GLSL types aliases. */
using vec2 = float2;
using vec3 = float3;
using vec4 = float4;
using mat2x2 = float2x2;
using mat2x3 = float2x3;
using mat2x4 = float2x4;
using mat3x2 = float3x2;
using mat3x3 = float3x3;
using mat3x4 = float3x4;
using mat4x2 = float4x2;
using mat4x3 = float4x3;
using mat4x4 = float4x4;
using mat2 = float2x2;
using mat3 = float3x3;
using mat4 = float4x4;
using ivec2 = int2;
using ivec3 = int3;
using ivec4 = int4;
using uvec2 = uint2;
using uvec3 = uint3;
using uvec4 = uint4;
using bvec2 = bool2;
using bvec3 = bool3;
using bvec4 = bool4;

/* Compute decorators. */
#define barrier() \
  threadgroup_barrier(mem_flags::mem_threadgroup | mem_flags::mem_device | mem_flags::mem_texture)

/** Shader atomics:
 * In order to emulate GLSL-style atomic operations, wherein variables can be used within atomic
 * operations, even if they are not explicitly declared atomic, we can cast the pointer to atomic,
 * to ensure that the load instruction follows atomic_load/store idioms.
 *
 * NOTE: We cannot hoist the address space into the template declaration, so these must be declared
 * for each relevant address space. */
#define ATOMIC_OP_EX(qualifier, glsl_op, mtl_op) \
  template<typename T> T atomic##glsl_op(qualifier T &mem, T data) \
  { \
    return atomic_##mtl_op##_explicit((qualifier _atomic<T> *)&mem, data, memory_order_relaxed); \
  }

#define ATOMIC_OP(glsl_op, mtl_op) \
  ATOMIC_OP_EX(threadgroup, glsl_op, mtl_op) \
  ATOMIC_OP_EX(device, glsl_op, mtl_op)

ATOMIC_OP(Max, fetch_max)
ATOMIC_OP(Min, fetch_min)
ATOMIC_OP(Add, fetch_add)
ATOMIC_OP(Sub, fetch_sub)
ATOMIC_OP(And, fetch_and)
ATOMIC_OP(Or, fetch_or)
ATOMIC_OP(Xor, fetch_xor)
ATOMIC_OP(Exchange, exchange)

#undef ATOMIC_OP

/* Generate wrapper structs for combined texture and sampler type. */
#ifdef USE_ARGUMENT_BUFFER_FOR_SAMPLERS
using sampler_ptr = constant sampler *;
#else
using sampler_ptr = thread sampler *;
#endif

/* Use point sampler instead of texture read to benefit from texture caching and reduce branching
 * through removal of bounds tests, as these are handled by the sample operation. */
constexpr sampler _mtl_fetch_samp(address::clamp_to_zero, filter::nearest, coord::pixel);

template<typename T,
         access A,
         typename TextureT,
         bool is_depth,
         int Dim,
         int Cube,
         int Array,
         bool Atomic = false>
struct _mtl_sampler {

  template<typename U, int S>
  using vec_or_scalar = typename metal::conditional<S == 1, U, vec<U, S>>::type;

  using FltCoord = vec_or_scalar<float, Dim + Cube + Array>;
  using FltDeriv = vec_or_scalar<float, Dim + Cube>;
  using IntCoord = vec_or_scalar<int, Dim + Cube + Array>;
  using IntDeriv = vec_or_scalar<int, Dim + Cube>;
  using UintCoord = vec_or_scalar<uint, Dim + Cube + Array>;
  using UintDeriv = vec_or_scalar<uint, Dim + Cube>;
  using SizeVec = vec_or_scalar<int, Dim + Array>;
  using DataVec = vec<T, 4>;
  using AtomicT = T;

  /* Template compatible gradient type choosing. */
  template<int D, int C> struct gradient_n {};
  /* clang-format off */
  template<> struct gradient_n<2, 0> { using type = gradient2d; };
  template<> struct gradient_n<3, 0> { using type = gradient3d; };
  template<> struct gradient_n<2, 1> { using type = gradientcube; };
  /* clang-format on */
  /* Using `using` would invalidate the whole class. */
#define gradient typename gradient_n<Dim, Cube>::type

#ifndef MTL_SUPPORTS_TEXTURE_ATOMICS
  /* If native texture atomics are unsupported, we instead utilize a custom type which wraps a
   * buffer-backed texture. This texture will always be a Texture2D, but will emulate access to
   * Texture3D and Texture2DArray by stacking layers.
   * Access pattern will be derived based on the source type. 2DArray and 3D atomic texture
   * support will require information on the size of each layer within the source 2D texture.
   *
   * A device pointer to the backing buffer will also be available for the atomic operations.
   * NOTE: For atomic ops, it will only be valid to use access::read_write.
   * We still need to use the wrapped type for access:sample, as texture2DArray and texture3D
   * will require access indirection.
   *
   * NOTE: Only type of UINT is valid, but full template provided to match syntax of standard
   * textures. */
  template<typename U, int D, bool At> struct AtomicEmulation {};

  template<typename U> struct AtomicEmulation<U, 2, true> {
    /** Buffer to do atomic operations on. */
    device U *buffer;
    /* Aligned width matches the number of buffer elements in bytes_per_row. This may be greater
     * than the texture's native width to satisfy device alignment rules. We need to use the padded
     * width when writing to ensure the correct writing location aligns with a given pixel location
     * in the texture. */
    ushort aligned_width;

    /* Convert 2D coordinates to the backing 2D texture coordinates. */
    int2 to_internal_coord(thread TextureT * /*texture*/, IntCoord coord) const
    {
      return coord.xy;
    }
    /* Convert 2D coordinates to the memory location in the 2D texture memory buffer. */
    uint to_linear_coord(thread TextureT * /*texture*/, IntCoord coord) const
    {
      return coord.x + coord.y * aligned_width;
    }
  };

  template<typename U> struct AtomicEmulation<U, 3, true> {
    /** Buffer to do atomic operations on. */
    device U *buffer;
    /**
     * Required for pixel location inside the backing texture 2D space, and texture size query.
     *
     * IMPORTANT: Note that the slices are not contiguous in the backing texture as we simply use
     * it for linear storage.
     * For instance, for a [2,2,2] texture store in a [6x4] backing texture:
     *
     * \code{.unparsed}
     * 001122
     * 334455
     * 667788
     * xxxxxx
     * \endcode
     *
     * The numbers are rows in the 3D texture.
     */
    ushort3 texture_size;
    /* Aligned width matches the number of buffer elements in bytes_per_row. This may be greater
     * than the texture's native width to satisfy device alignment rules. We need to use the padded
     * width when writing to ensure the correct writing location aligns with a given pixel location
     * in the texture. */
    ushort aligned_width;

    /* Convert 3D coordinates to the backing 2D texture coordinates. */
    int2 to_internal_coord(thread TextureT *texture, IntCoord coord) const
    {
      /* Index of a pixel in the data array. Assuming data layout is scan-line. */
      uint linear_pixel = uint(coord.x) + uint(coord.y) * texture_size.x +
                          uint(coord.z) * (texture_size.x * texture_size.y);

      uint backing_tex_width = texture->get_width();
      /* Coordinate inside the backing texture. */
      uint y = linear_pixel / backing_tex_width;
      /* Avoid use of modulo operator for speed. */
      uint x = linear_pixel - y * backing_tex_width;
      return int2(x, y);
    }

    /* Convert 3D coordinates to the memory location in the 2D texture memory buffer. */
    uint to_linear_coord(thread TextureT *texture, IntCoord coord) const
    {
      uint2 co = uint2(to_internal_coord(texture, coord));
      return co.x + co.y * uint(aligned_width);
    }
  };

  AtomicEmulation<T, Dim + Array, Atomic> atomic;
#endif

  thread TextureT *texture;
  sampler_ptr samp;

  template<int dim, int array, bool At = false> SizeVec size_impl(uint lod) const {}
  template<> SizeVec size_impl<1, 0>(uint lod) const
  {
    return SizeVec(texture->get_width());
  }
  template<> SizeVec size_impl<1, 1>(uint lod) const
  {
    return SizeVec(texture->get_width(), texture->get_array_size());
  }
  template<> SizeVec size_impl<2, 0>(uint lod) const
  {
    return SizeVec(texture->get_width(lod), texture->get_height(lod));
  }
  template<> SizeVec size_impl<2, 1>(uint lod) const
  {
    return SizeVec(texture->get_width(lod), texture->get_height(lod), texture->get_array_size());
  }
  template<> SizeVec size_impl<3, 0>(uint lod) const
  {
    return SizeVec(texture->get_width(lod), texture->get_height(lod), texture->get_depth(lod));
  }
#ifndef MTL_SUPPORTS_TEXTURE_ATOMICS
  template<> SizeVec size_impl<2, 1, true>(uint lod) const
  {
    return SizeVec(atomic.texture_size);
  }
  template<> SizeVec size_impl<3, 0, true>(uint lod) const
  {
    return SizeVec(atomic.texture_size);
  }
#endif
  SizeVec size(int lod = 0) const
  {
    return size_impl<Dim, Array, Atomic>(uint(lod));
  }

#define ARRAY_FN \
  template<int Ar = Array, bool At = Atomic, ENABLE_IF(Ar == 1), ENABLE_IF(At == false)>
#define NON_ARRAY_FN \
  template<int Ar = Array, bool At = Atomic, ENABLE_IF(Ar == 0), ENABLE_IF(At == false)>

  NON_ARRAY_FN DataVec sample(FltCoord coord) const
  {
    return texture->sample(*samp, coord);
  }
  NON_ARRAY_FN DataVec sample_grad(FltCoord coord, FltDeriv dPdx, FltDeriv dPdy) const
  {
    return texture->sample(*samp, coord, gradient(dPdx, dPdy));
  }
  NON_ARRAY_FN DataVec sample_bias(FltCoord coord, bias lod_bias) const
  {
    return texture->sample(*samp, coord, lod_bias);
  }
  NON_ARRAY_FN DataVec sample_lod(FltCoord coord, level lod, IntDeriv offset = {0}) const
  {
    return texture->sample(*samp, coord, lod, offset);
  }
  NON_ARRAY_FN DataVec gather(FltCoord coord) const
  {
    return texture->gather(*samp, coord);
  }
  NON_ARRAY_FN DataVec fetch(IntCoord coord) const
  {
    return texture->sample(_mtl_fetch_samp, FltCoord(coord));
  }
  NON_ARRAY_FN DataVec fetch(IntCoord coord, level lod, IntDeriv offset = {0}) const
  {
    return texture->sample(_mtl_fetch_samp, FltCoord(coord), lod, offset);
  }

  ARRAY_FN DataVec sample(FltCoord coord) const
  {
    return texture->sample(*samp, uv_mask(coord), layer_mask(coord));
  }
  ARRAY_FN DataVec sample_grad(FltCoord coord, FltDeriv dPdx, FltDeriv dPdy) const
  {
    return texture->sample(*samp, uv_mask(coord), layer_mask(coord), gradient(dPdx, dPdy));
  }
  ARRAY_FN DataVec sample_bias(FltCoord coord, bias lod_bias) const
  {
    return texture->sample(*samp, uv_mask(coord), layer_mask(coord), lod_bias);
  }
  ARRAY_FN DataVec sample_lod(FltCoord coord, level lod, IntDeriv offset = {0}) const
  {
    return texture->sample(*samp, uv_mask(coord), layer_mask(coord), lod, offset);
  }
  ARRAY_FN DataVec gather(FltCoord coord) const
  {
    return texture->gather(*samp, uv_mask(coord), layer_mask(coord));
  }
  ARRAY_FN DataVec fetch(IntCoord coord) const
  {
    return texture->sample(_mtl_fetch_samp, uv_mask(coord), layer_mask(coord));
  }
  ARRAY_FN DataVec fetch(IntCoord coord, level lod, IntDeriv ofs = {0}) const
  {
    return texture->sample(_mtl_fetch_samp, uv_mask(coord), layer_mask(coord), lod, ofs);
  }

#undef gradient
#undef ARRAY_FN
#undef NON_ARRAY_FN

  /**
   * Image functions.
   * To be split to its own class.
   */

#define ARRAY_FN \
  template<int Ar = Array, bool At = Atomic, ENABLE_IF(Ar == 1), ENABLE_IF(At == false)>
#define NON_ARRAY_FN \
  template<int Ar = Array, bool At = Atomic, ENABLE_IF(Ar == 0), ENABLE_IF(At == false)>

  NON_ARRAY_FN DataVec load(IntCoord coord) const
  {
    return texture->read(UintCoord(coord), 0);
  }
  NON_ARRAY_FN void store(DataVec data, IntCoord coord) const
  {
    texture->write(data, UintCoord(coord), 0);
  }

  ARRAY_FN DataVec load(IntCoord coord) const
  {
    return texture->read(uv_mask_img(coord), layer_mask_img(coord), 0);
  }
  ARRAY_FN void store(DataVec data, IntCoord coord) const
  {
    texture->write(data, uv_mask_img(coord), layer_mask_img(coord), 0);
  }

#undef ARRAY_FN
#undef NON_ARRAY_FN

#ifndef MTL_SUPPORTS_TEXTURE_ATOMICS
  /* Atomic samplers only support `texelFetch` as the texture layout doesn't allow filtering. */

#  define ATOMIC_FN template<bool At = Atomic, ENABLE_IF(At == true)>

  ATOMIC_FN DataVec fetch(IntCoord coord) const
  {
    int2 coord_2d = atomic.to_internal_coord(texture, coord);
    return texture->sample(_mtl_fetch_samp, float2(coord_2d));
  }
  ATOMIC_FN DataVec fetch(IntCoord coord, level lod, IntDeriv offset = {0}) const
  {
    int2 coord_2d = atomic.to_internal_coord(texture, coord);
    return texture->sample(_mtl_fetch_samp, float2(coord_2d), lod, offset);
  }

  ATOMIC_FN DataVec load(IntCoord coord) const
  {
    int2 coord_2d = atomic.to_internal_coord(texture, coord);
    return texture->read(uint2(coord_2d), 0);
  }
  ATOMIC_FN void store(DataVec data, IntCoord coord) const
  {
    int2 coord_2d = atomic.to_internal_coord(texture, coord);
    texture->write(data, uint2(coord_2d), 0);
  }

#  undef ATOMIC_FN

  AtomicT atomic_min(IntCoord coord, AtomicT data) const
  {
    return atomicMin(atomic.buffer[atomic.to_linear_coord(texture, coord)], data);
  }
  AtomicT atomic_max(IntCoord coord, AtomicT data) const
  {
    return atomicMax(atomic.buffer[atomic.to_linear_coord(texture, coord)], data);
  }
  AtomicT atomic_add(IntCoord coord, AtomicT data) const
  {
    return atomicAdd(atomic.buffer[atomic.to_linear_coord(texture, coord)], data);
  }
  AtomicT atomic_and(IntCoord coord, AtomicT data) const
  {
    return atomicAnd(atomic.buffer[atomic.to_linear_coord(texture, coord)], data);
  }
  AtomicT atomic_or(IntCoord coord, AtomicT data) const
  {
    return atomicOr(atomic.buffer[atomic.to_linear_coord(texture, coord)], data);
  }
  AtomicT atomic_xor(IntCoord coord, AtomicT data) const
  {
    return atomicXor(atomic.buffer[atomic.to_linear_coord(texture, coord)], data);
  }
  AtomicT atomic_exchange(IntCoord coord, AtomicT data) const
  {
    return atomicExchange(atomic.buffer[atomic.to_linear_coord(texture, coord)], data);
  }

#else
#  define NON_ARRAY_ATOMIC \
    template<typename U = T, \
             int Ar = Array, \
             ENABLE_IF(metal::is_integral_v<U> == true), \
             ENABLE_IF(sizeof(U) == 4), \
             ENABLE_IF(Ar == 0)>
#  define ARRAY_ATOMIC \
    template<typename U = T, \
             int Ar = Array, \
             ENABLE_IF(metal::is_integral_v<U> == true), \
             ENABLE_IF(sizeof(U) == 4), \
             ENABLE_IF(Ar == 1)>

  NON_ARRAY_ATOMIC AtomicT atomic_min(IntCoord coord, AtomicT data) const
  {
    return texture->atomic_fetch_min(UintCoord(coord), data).x;
  }
  NON_ARRAY_ATOMIC AtomicT atomic_max(IntCoord coord, AtomicT data) const
  {
    return texture->atomic_fetch_max(UintCoord(coord), data).x;
  }
  NON_ARRAY_ATOMIC AtomicT atomic_add(IntCoord coord, AtomicT data) const
  {
    return texture->atomic_fetch_add(UintCoord(coord), data).x;
  }
  NON_ARRAY_ATOMIC AtomicT atomic_and(IntCoord coord, AtomicT data) const
  {
    return texture->atomic_fetch_and(UintCoord(coord), data).x;
  }
  NON_ARRAY_ATOMIC AtomicT atomic_or(IntCoord coord, AtomicT data) const
  {
    return texture->atomic_fetch_or(UintCoord(coord), data).x;
  }
  NON_ARRAY_ATOMIC AtomicT atomic_xor(IntCoord coord, AtomicT data) const
  {
    return texture->atomic_fetch_xor(UintCoord(coord), data).x;
  }
  NON_ARRAY_ATOMIC AtomicT atomic_exchange(IntCoord coord, AtomicT data) const
  {
    return texture->atomic_exchange(UintCoord(coord), data).x;
  }

  ARRAY_ATOMIC AtomicT atomic_min(IntCoord coord, AtomicT data) const
  {
    return texture->atomic_fetch_min(uv_mask_img(coord), layer_mask_img(coord), data).x;
  }
  ARRAY_ATOMIC AtomicT atomic_max(IntCoord coord, AtomicT data) const
  {
    return texture->atomic_fetch_max(uv_mask_img(coord), layer_mask_img(coord), data).x;
  }
  ARRAY_ATOMIC AtomicT atomic_add(IntCoord coord, AtomicT data) const
  {
    return texture->atomic_fetch_add(uv_mask_img(coord), layer_mask_img(coord), data).x;
  }
  ARRAY_ATOMIC AtomicT atomic_and(IntCoord coord, AtomicT data) const
  {
    return texture->atomic_fetch_and(uv_mask_img(coord), layer_mask_img(coord), data).x;
  }
  ARRAY_ATOMIC AtomicT atomic_or(IntCoord coord, AtomicT data) const
  {
    return texture->atomic_fetch_or(uv_mask_img(coord), layer_mask_img(coord), data).x;
  }
  ARRAY_ATOMIC AtomicT atomic_xor(IntCoord coord, AtomicT data) const
  {
    return texture->atomic_fetch_xor(uv_mask_img(coord), layer_mask_img(coord), data).x;
  }
  ARRAY_ATOMIC AtomicT atomic_exchange(IntCoord coord, AtomicT data) const
  {
    return texture->atomic_exchange(uv_mask_img(coord), layer_mask_img(coord), data).x;
  }

#  undef NON_ARRAY_ATOMIC
#  undef ARRAY_ATOMIC
#endif

  void fence()
  {
    texture->fence();
  }

 private:
  template<typename U, typename V> static U reshape(V v) {}
  /* clang-format off */
  template<> float reshape<float>(float2 v) { return v.x; }
  template<> float2 reshape<float2>(float3 v) { return v.xy; }
  template<> float3 reshape<float3>(float4 v) { return v.xyz; }
  template<> int reshape<int>(int2 v) { return v.x; }
  template<> int2 reshape<int2>(int3 v) { return v.xy; }
  template<> int3 reshape<int3>(int4 v) { return v.xyz; }
  /* clang-format on */

  FltDeriv uv_mask(FltCoord coord) const
  {
    return reshape<FltDeriv>(coord);
  }
  FltDeriv uv_mask(IntCoord coord) const
  {
    return FltDeriv(reshape<IntDeriv>(coord));
  }

  uint layer_mask(FltCoord coord) const
  {
    return coord[Dim + Cube];
  }
  uint layer_mask(IntCoord coord) const
  {
    return coord[Dim + Cube];
  }

  UintDeriv uv_mask_img(IntCoord coord) const
  {
    return UintDeriv(reshape<IntDeriv>(coord));
  }

  uint layer_mask_img(IntCoord coord) const
  {
    return coord[Dim + Cube];
  }
};

/** Sampler functions */

#define SAMPLER_FN \
  template<typename SamplerT, \
           typename FltCoord = typename SamplerT::FltCoord, \
           typename FltDeriv = typename SamplerT::FltDeriv, \
           typename IntCoord = typename SamplerT::IntCoord, \
           typename IntDeriv = typename SamplerT::IntDeriv, \
           typename UintCoord = typename SamplerT::UintCoord, \
           typename UintDeriv = typename SamplerT::UintDeriv, \
           typename SizeVec = typename SamplerT::SizeVec, \
           typename DataVec = typename SamplerT::DataVec>

SAMPLER_FN SizeVec textureSize(SamplerT texture, int lod)
{
  return texture.size(lod);
}

SAMPLER_FN DataVec texture(SamplerT texture, FltCoord coord)
{
  return texture.sample(coord);
}

SAMPLER_FN DataVec texture(SamplerT texture, FltCoord coord, float lod_bias)
{
  return texture.sample_bias(coord, bias(lod_bias));
}

SAMPLER_FN DataVec textureLod(SamplerT texture, FltCoord coord, float lod)
{
  return texture.sample_lod(coord, level(lod));
}

SAMPLER_FN DataVec textureLodOffset(SamplerT texture, FltCoord coord, float lod, IntDeriv offset)
{
  return texture.sample_lod(coord, level(lod), offset);
}

SAMPLER_FN DataVec textureGather(SamplerT texture, FltCoord coord)
{
  return texture.gather(coord);
}

SAMPLER_FN DataVec textureGrad(SamplerT texture, FltCoord coord, FltDeriv dPdx, FltDeriv dPdy)
{
  return texture.sample_grad(coord, dPdx, dPdy);
}

SAMPLER_FN DataVec texelFetch(SamplerT texture, IntCoord coord, int lod)
{
  return texture.fetch(coord, level(lod));
}

SAMPLER_FN DataVec texelFetchOffset(SamplerT texture, IntCoord coord, int lod, IntDeriv offset)
{
  return texture.fetch(coord, level(lod), offset);
}

#undef SAMPLER_FN

/** Image functions */

#define IMAGE_FN \
  template<typename SamplerT, \
           typename IntCoord = typename SamplerT::IntCoord, \
           typename IntDeriv = typename SamplerT::IntDeriv, \
           typename UintCoord = typename SamplerT::UintCoord, \
           typename UintDeriv = typename SamplerT::UintDeriv, \
           typename SizeVec = typename SamplerT::SizeVec, \
           typename DataVec = typename SamplerT::DataVec, \
           typename AtomicT = typename SamplerT::AtomicT>

IMAGE_FN SizeVec imageSize(SamplerT texture)
{
  return texture.size();
}

IMAGE_FN void imageFence(SamplerT texture)
{
  return texture.fence();
}

IMAGE_FN DataVec imageLoad(SamplerT texture, IntCoord coord)
{
  if (any(UintCoord(coord) >= UintCoord(texture.size()))) {
    return DataVec(0);
  }
  return texture.load(coord);
}

IMAGE_FN DataVec imageLoadFast(SamplerT texture, IntCoord coord)
{
  return texture.load(coord);
}

IMAGE_FN void imageStore(SamplerT texture, IntCoord coord, DataVec data)
{
  if (any(UintCoord(coord) >= UintCoord(texture.size()))) {
    return;
  }
  texture.store(data, coord);
}

IMAGE_FN
void imageStoreFast(SamplerT texture, IntCoord coord, DataVec data)
{
  texture.store(data, coord);
}

IMAGE_FN AtomicT imageAtomicMin(SamplerT texture, IntCoord coord, AtomicT data)
{
  if (any(UintCoord(coord) >= UintCoord(texture.size()))) {
    return AtomicT(0);
  }
  return texture.atomic_min(coord, data);
}
IMAGE_FN AtomicT imageAtomicMax(SamplerT texture, IntCoord coord, AtomicT data)
{
  if (any(UintCoord(coord) >= UintCoord(texture.size()))) {
    return AtomicT(0);
  }
  return texture.atomic_max(coord, data);
}
IMAGE_FN AtomicT imageAtomicAdd(SamplerT texture, IntCoord coord, AtomicT data)
{
  if (any(UintCoord(coord) >= UintCoord(texture.size()))) {
    return AtomicT(0);
  }
  return texture.atomic_add(coord, data);
}
IMAGE_FN AtomicT imageAtomicAnd(SamplerT texture, IntCoord coord, AtomicT data)
{
  if (any(UintCoord(coord) >= UintCoord(texture.size()))) {
    return AtomicT(0);
  }
  return texture.atomic_and(coord, data);
}
IMAGE_FN AtomicT imageAtomicOr(SamplerT texture, IntCoord coord, AtomicT data)
{
  if (any(UintCoord(coord) >= UintCoord(texture.size()))) {
    return AtomicT(0);
  }
  return texture.atomic_or(coord, data);
}
IMAGE_FN AtomicT imageAtomicXor(SamplerT texture, IntCoord coord, AtomicT data)
{
  if (any(UintCoord(coord) >= UintCoord(texture.size()))) {
    return AtomicT(0);
  }
  return texture.atomic_xor(coord, data);
}
IMAGE_FN AtomicT imageAtomicExchange(SamplerT texture, IntCoord coord, AtomicT data)
{
  if (any(UintCoord(coord) >= UintCoord(texture.size()))) {
    return AtomicT(0);
  }
  return texture.atomic_exchange(coord, data);
}

#undef IMAGE_FN

/* Add any types as needed. */
#define TEMPLATE template<typename T = float, access A = access::sample>
TEMPLATE using sampler2DDepth = _mtl_sampler<T, A, depth2d<T, A>, true, 2, 0, 0>;
TEMPLATE using sampler2DArrayDepth = _mtl_sampler<T, A, depth2d_array<T, A>, true, 2, 0, 1>;
TEMPLATE using depthCube = _mtl_sampler<T, A, texturecube<T, A>, true, 2, 1, 1>;
TEMPLATE using depthCubeArray = _mtl_sampler<T, A, texturecube_array<T, A>, true, 2, 1, 1>;
TEMPLATE using sampler1D = _mtl_sampler<T, A, texture1d<T, A>, false, 1, 0, 0>;
TEMPLATE using sampler1DArray = _mtl_sampler<T, A, texture1d_array<T, A>, false, 1, 0, 1>;
TEMPLATE using sampler2D = _mtl_sampler<T, A, texture2d<T, A>, false, 2, 0, 0>;
TEMPLATE using sampler2DArray = _mtl_sampler<T, A, texture2d_array<T, A>, false, 2, 0, 1>;
TEMPLATE using sampler3D = _mtl_sampler<T, A, texture3d<T, A>, false, 3, 0, 0>;
TEMPLATE using samplerBuffer = _mtl_sampler<T, A, texture_buffer<T, A>, false, 1, 0, 0>;
TEMPLATE using samplerCube = _mtl_sampler<T, A, texturecube<T, A>, false, 2, 1, 0>;
TEMPLATE using samplerCubeArray = _mtl_sampler<T, A, texturecube_array<T, A>, false, 2, 1, 1>;
/* Atomic textures are defined as 2D textures with special layout for 3D texture emulation. */
TEMPLATE using sampler2DAtomic = _mtl_sampler<T, A, texture2d<T, A>, false, 2, 0, 0, true>;
TEMPLATE using sampler2DArrayAtomic = _mtl_sampler<T, A, texture2d<T, A>, false, 2, 0, 1, true>;
TEMPLATE using sampler3DAtomic = _mtl_sampler<T, A, texture2d<T, A>, false, 3, 0, 0, true>;

/* Used by backend to declare the samplers. Could be removed. */
TEMPLATE using _mtl_sampler_depth_2d = sampler2DDepth<T, A>;
TEMPLATE using _mtl_sampler_depth_2d_array = sampler2DArrayDepth<T, A>;
TEMPLATE using _mtl_sampler_depth_cube = depthCube<T, A>;
TEMPLATE using _mtl_sampler_depth_cube_array = depthCubeArray<T, A>;
TEMPLATE using _mtl_sampler_1d = sampler1D<T, A>;
TEMPLATE using _mtl_sampler_1d_array = sampler1DArray<T, A>;
TEMPLATE using _mtl_sampler_2d = sampler2D<T, A>;
TEMPLATE using _mtl_sampler_2d_array = sampler2DArray<T, A>;
TEMPLATE using _mtl_sampler_3d = sampler3D<T, A>;
TEMPLATE using _mtl_sampler_buffer = samplerBuffer<T, A>;
TEMPLATE using _mtl_sampler_cube = samplerCube<T, A>;
TEMPLATE using _mtl_sampler_cube_array = samplerCubeArray<T, A>;
TEMPLATE using _mtl_sampler_2d_atomic = sampler2DAtomic<T, A>;
TEMPLATE using _mtl_sampler_2d_array_atomic = sampler2DArrayAtomic<T, A>;
TEMPLATE using _mtl_sampler_3d_atomic = sampler3DAtomic<T, A>;
#undef TEMPLATE

/* Variant for 1D samplers. Discard the lod. */
template<typename T, access A>
typename sampler1D<T, A>::DataVec texelFetch(sampler1D<T, A> texture, int coord, int lod = 0)
{
  return texture.fetch(coord);
}

/* Variant for 1DArray samplers. Discard the lod. */
template<typename T, access A>
typename sampler1DArray<T, A>::DataVec texelFetch(sampler1DArray<T, A> texture,
                                                  int2 coord,
                                                  int lod = 0)
{
  return texture.fetch(coord);
}

/* Variant for buffer samplers. Discard the lod. */
template<typename T, access A>
typename samplerBuffer<T, A>::DataVec texelFetch(samplerBuffer<T, A> texture,
                                                 int coord,
                                                 int lod = 0)
{
  uint texel = uint(coord);
  if (texel < texture.texture->get_width()) {
    return texture.texture->read(texel);
  }
  return typename samplerBuffer<T, A>::DataVec(0);
}

/* Sampler struct for argument buffer. */
#ifdef USE_ARGUMENT_BUFFER_FOR_SAMPLERS
struct SStruct {
  array<sampler, ARGUMENT_BUFFER_NUM_SAMPLERS> sampler_args [[id(0)]];
};
#endif

/* Samplers as function parameters. */
#define sampler1D thread _mtl_sampler_1d<float>
#define sampler1DArray thread _mtl_sampler_1d_array<float>
#define sampler2D thread _mtl_sampler_2d<float>
#define sampler2DDepth thread _mtl_sampler_depth_2d<float>
#define sampler2DShadow thread _mtl_sampler_depth_2d<float>
#define sampler2DArray thread _mtl_sampler_2d_array<float>
#define sampler2DArrayDepth thread _mtl_sampler_depth_2d_array<float>
#define sampler2DArrayShadow thread _mtl_sampler_depth_2d_array<float>
#define sampler3D thread _mtl_sampler_3d<float>
#define samplerBuffer thread _mtl_sampler_buffer<float, access::read>
#define samplerCube thread _mtl_sampler_cube<float>
#define samplerCubeDepth thread _mtl_sampler_depth_cube<float>
#define samplerCubeShadow thread _mtl_sampler_depth_cube<float>
#define samplerCubeArray thread _mtl_sampler_cube_array<float>
#define samplerCubeArrayDepth thread _mtl_sampler_depth_cube_array<float>
#define samplerCubeArrayShadow thread _mtl_sampler_depth_cube_array<float>

#define usampler1D thread _mtl_sampler_1d<uint>
#define usampler1DArray thread _mtl_sampler_1d_array<uint>
#define usampler2D thread _mtl_sampler_2d<uint>
#define usampler2DArray thread _mtl_sampler_2d_array<uint>
#define usampler3D thread _mtl_sampler_3d<uint>
#define usamplerBuffer thread _mtl_sampler_buffer<uint, access::read>
#define usamplerCube thread _mtl_sampler_cube<uint>
#define usamplerCubeArray thread _mtl_sampler_cube_array<uint>

#define isampler1D thread _mtl_sampler_1d<int>
#define isampler1DArray thread _mtl_sampler_1d_array<int>
#define isampler2D thread _mtl_sampler_2d<int>
#define isampler2DArray thread _mtl_sampler_2d_array<int>
#define isampler3D thread _mtl_sampler_3d<int>
#define isamplerBuffer thread _mtl_sampler_buffer<int, access::read>
#define isamplerCube thread _mtl_sampler_cube<int>
#define isamplerCubeArray thread _mtl_sampler_cube_array<int>

#ifndef MTL_SUPPORTS_TEXTURE_ATOMICS
/* If texture atomics are unsupported, map atomic types to internal atomic fallback type. */
#  define usampler2DArrayAtomic _mtl_sampler_2d_array_atomic<uint>
#  define usampler2DAtomic _mtl_sampler_2d_atomic<uint>
#  define usampler3DAtomic _mtl_sampler_3d_atomic<uint>
#  define isampler2DArrayAtomic _mtl_sampler_2d_array_atomic<int>
#  define isampler2DAtomic _mtl_sampler_2d_atomic<int>
#  define isampler3DAtomic _mtl_sampler_3d_atomic<int>
#else
#  define usampler2DArrayAtomic usampler2DArray
#  define usampler2DAtomic usampler2D
#  define usampler3DAtomic usampler3D
#  define isampler2DArrayAtomic isampler2DArray
#  define isampler2DAtomic isampler2D
#  define isampler3DAtomic isampler3D
#endif

/* Vector accessor aliases. */
#define st xy

/* Matrix compare operators. */
#define EQ_OP(type, ...) \
  inline bool operator==(type a, type b) \
  { \
    return __VA_ARGS__; \
  }
EQ_OP(float2x2, all(a[0] == b[0]) && all(a[1] == b[1]))
EQ_OP(float2x3, all(a[0] == b[0]) && all(a[1] == b[1]))
EQ_OP(float2x4, all(a[0] == b[0]) && all(a[1] == b[1]))
EQ_OP(float3x2, all(a[0] == b[0]) && all(a[1] == b[1]) && all(a[2] == b[2]))
EQ_OP(float3x3, all(a[0] == b[0]) && all(a[1] == b[1]) && all(a[2] == b[2]))
EQ_OP(float3x4, all(a[0] == b[0]) && all(a[1] == b[1]) && all(a[2] == b[2]))
EQ_OP(float4x2, all(a[0] == b[0]) && all(a[1] == b[1]) && all(a[2] == b[2]) && all(a[3] == b[3]))
EQ_OP(float4x3, all(a[0] == b[0]) && all(a[1] == b[1]) && all(a[2] == b[2]) && all(a[3] == b[3]))
EQ_OP(float4x4, all(a[0] == b[0]) && all(a[1] == b[1]) && all(a[2] == b[2]) && all(a[3] == b[3]))
#undef EQ_OP

template<int N, int M> inline bool operator!=(matrix<float, N, M> a, matrix<float, N, M> b)
{
  return !(a == b);
}

/* Matrix unary minus operator. */
template<int N, int M> inline matrix<float, N, M> operator-(matrix<float, N, M> a)
{
  return a * -1.0f;
}

/* Common Functions. */
#define dFdx(x) dfdx(x)
#define dFdy(x) dfdy(x)
#define discard discard_fragment()
#define inversesqrt rsqrt

/* clang-format off */
inline float radians(float deg) { return deg * 0.01745329251f; /* M_PI_F / 180 */ }
inline float degrees(float rad) { return rad * 57.2957795131f; /* 180 / M_PI_F */ }
inline int floatBitsToInt(float f) { return as_type<int>(f); }
inline int2 floatBitsToInt(float2 f) { return as_type<int2>(f); }
inline int3 floatBitsToInt(float3 f) { return as_type<int3>(f); }
inline int4 floatBitsToInt(float4 f) { return as_type<int4>(f); }
inline uint floatBitsToUint(float f) { return as_type<uint>(f); }
inline uint2 floatBitsToUint(float2 f) { return as_type<uint2>(f); }
inline uint3 floatBitsToUint(float3 f) { return as_type<uint3>(f); }
inline uint4 floatBitsToUint(float4 f) { return as_type<uint4>(f); }
inline float intBitsToFloat(int f) { return as_type<float>(f); }
inline float2 intBitsToFloat(int2 f) { return as_type<float2>(f); }
inline float3 intBitsToFloat(int3 f) { return as_type<float3>(f); }
inline float4 intBitsToFloat(int4 f) { return as_type<float4>(f); }
inline float uintBitsToFloat(uint f) { return as_type<float>(f); }
inline float2 uintBitsToFloat(uint2 f) { return as_type<float2>(f); }
inline float3 uintBitsToFloat(uint3 f) { return as_type<float3>(f); }
inline float4 uintBitsToFloat(uint4 f) { return as_type<float4>(f); }
/* clang-format on */

#define bitfieldReverse reverse_bits
#define bitfieldExtract extract_bits
#define bitfieldInsert insert_bits

/* popcount returns the same type as T. bitCount always returns int. */
template<typename T> int bitCount(T x)
{
  return int(popcount(x));
}
template<typename T, int n> vec<int, n> bitCount(vec<T, n> x)
{
  return vec<int, n>(popcount(x));
}

template<typename T> int findLSB(T x)
{
  /* ctz returns the number of trailing zeroes. To fetch the index of the LSB, we can also use this
   * value as index, however we need to filter out the case where the input value is zero to match
   * GLSL functionality. */
  return (x == T(0)) ? int(-1) : int(ctz(x));
}

template<typename T> int findMSB(T x)
{
  /* clz returns the number of leading zeroes. To fetch the index of the MSB, we can also use this
   * value as index when offset by 1. */
  return int(sizeof(T) * 8) - 1 - int(clz(x));
}

#ifndef MTL_SUPPORTS_TEXTURE_ATOMICS
/* textureSize functions for fallback atomic textures. */
template<typename T, access A>
int2 textureSize(thread _mtl_sampler_2d_atomic<T, A> image, uint lod)
{
  return int2(image.texture->get_width(lod), image.texture->get_height(lod));
}

template<typename T, access A>
int3 textureSize(thread _mtl_sampler_2d_array_atomic<T, A> image, uint lod)
{
  return int3(image.texture_size);
}

template<typename T, access A>
int3 textureSize(thread _mtl_sampler_3d_atomic<T, A> image, uint lod)
{
  return int3(image.texture_size);
}
#endif

#define unpackUnorm4x8 unpack_unorm4x8_to_float
#define unpackSnorm4x8 unpack_snorm4x8_to_float
#define unpackUnorm2x16 unpack_unorm2x16_to_float
#define unpackSnorm2x16 unpack_snorm2x16_to_float

/* Equality and comparison functions. */
#define lessThan(a, b) ((a) < (b))
#define lessThanEqual(a, b) ((a) <= (b))
#define greaterThan(a, b) ((a) > (b))
#define greaterThanEqual(a, b) ((a) >= (b))
#define equal(a, b) ((a) == (b))
#define notEqual(a, b) ((a) != (b))

/* Modulo functionality. */
/* `mod(x, y)` is defined as `x - (y * floor(x / y))` in the metal specification.
 * This is not compatible with GLSL implementation. So we override it with a compatible one. */
#define mod(x, y) _compatible_mod(x, y)
#define MOD \
  { \
    return x - y * floor(x / y); \
  }
float _compatible_mod(float x, float y) MOD;
template<int S> vec<float, S> _compatible_mod(vec<float, S> x, float y) MOD;
template<int S> vec<float, S> _compatible_mod(vec<float, S> x, vec<float, S> y) MOD;
#undef MOD

/* Mathematical functions. */
template<typename T> T atan(T y, T x)
{
  return atan2(y, x);
}

/* Matrix Inverse. */
float4x4 inverse(float4x4 a)
{
  float b00 = a[0][0] * a[1][1] - a[0][1] * a[1][0];
  float b01 = a[0][0] * a[1][2] - a[0][2] * a[1][0];
  float b02 = a[0][0] * a[1][3] - a[0][3] * a[1][0];
  float b03 = a[0][1] * a[1][2] - a[0][2] * a[1][1];
  float b04 = a[0][1] * a[1][3] - a[0][3] * a[1][1];
  float b05 = a[0][2] * a[1][3] - a[0][3] * a[1][2];
  float b06 = a[2][0] * a[3][1] - a[2][1] * a[3][0];
  float b07 = a[2][0] * a[3][2] - a[2][2] * a[3][0];
  float b08 = a[2][0] * a[3][3] - a[2][3] * a[3][0];
  float b09 = a[2][1] * a[3][2] - a[2][2] * a[3][1];
  float b10 = a[2][1] * a[3][3] - a[2][3] * a[3][1];
  float b11 = a[2][2] * a[3][3] - a[2][3] * a[3][2];
  float4x4 adjoint;
  adjoint[0][0] = a[1][1] * b11 - a[1][2] * b10 + a[1][3] * b09;
  adjoint[0][1] = a[0][2] * b10 - a[0][1] * b11 - a[0][3] * b09;
  adjoint[0][2] = a[3][1] * b05 - a[3][2] * b04 + a[3][3] * b03;
  adjoint[0][3] = a[2][2] * b04 - a[2][1] * b05 - a[2][3] * b03;
  adjoint[1][0] = a[1][2] * b08 - a[1][0] * b11 - a[1][3] * b07;
  adjoint[1][1] = a[0][0] * b11 - a[0][2] * b08 + a[0][3] * b07;
  adjoint[1][2] = a[3][2] * b02 - a[3][0] * b05 - a[3][3] * b01;
  adjoint[1][3] = a[2][0] * b05 - a[2][2] * b02 + a[2][3] * b01;
  adjoint[2][0] = a[1][0] * b10 - a[1][1] * b08 + a[1][3] * b06;
  adjoint[2][1] = a[0][1] * b08 - a[0][0] * b10 - a[0][3] * b06;
  adjoint[2][2] = a[3][0] * b04 - a[3][1] * b02 + a[3][3] * b00;
  adjoint[2][3] = a[2][1] * b02 - a[2][0] * b04 - a[2][3] * b00;
  adjoint[3][0] = a[1][1] * b07 - a[1][0] * b09 - a[1][2] * b06;
  adjoint[3][1] = a[0][0] * b09 - a[0][1] * b07 + a[0][2] * b06;
  adjoint[3][2] = a[3][1] * b01 - a[3][0] * b03 - a[3][2] * b00;
  adjoint[3][3] = a[2][0] * b03 - a[2][1] * b01 + a[2][2] * b00;
  float determinant = b00 * b11 - b01 * b10 + b02 * b09 + b03 * b08 - b04 * b07 + b05 * b06;
  /* Multiplying by inverse since matrix types don't have divide operators. */
  return adjoint * (1.0f / determinant);
}

float3x3 inverse(float3x3 m)
{
  float3x3 adjoint;
  adjoint[0][0] = +(m[1][1] * m[2][2] - m[2][1] * m[1][2]);
  adjoint[0][1] = -(m[0][1] * m[2][2] - m[2][1] * m[0][2]);
  adjoint[0][2] = +(m[0][1] * m[1][2] - m[1][1] * m[0][2]);
  adjoint[1][0] = -(m[1][0] * m[2][2] - m[2][0] * m[1][2]);
  adjoint[1][1] = +(m[0][0] * m[2][2] - m[2][0] * m[0][2]);
  adjoint[1][2] = -(m[0][0] * m[1][2] - m[1][0] * m[0][2]);
  adjoint[2][0] = +(m[1][0] * m[2][1] - m[2][0] * m[1][1]);
  adjoint[2][1] = -(m[0][0] * m[2][1] - m[2][0] * m[0][1]);
  adjoint[2][2] = +(m[0][0] * m[1][1] - m[1][0] * m[0][1]);
  float determinant = m[0][0] * adjoint[0][0] + m[1][0] * adjoint[0][1] + m[2][0] * adjoint[0][2];
  /* Multiplying by inverse since matrix types don't have divide operators. */
  return adjoint * (1.0f / determinant);
}

float2x2 inverse(float2x2 m)
{
  float2x2 adjoint;
  adjoint[0][0] = +m[1][1];
  adjoint[1][0] = -m[1][0];
  adjoint[0][1] = -m[0][1];
  adjoint[1][1] = +m[0][0];
  float determinant = m[0][0] * m[1][1] - m[1][0] * m[0][1];
  /* Multiplying by inverse since matrix types don't have divide operators. */
  return adjoint * (1.0f / determinant);
}

/* Additional overloads for builtin functions. */
float distance(float x, float y)
{
  return abs(y - x);
}

/* Overload for mix(vec<T>, vec<T>, float). */
template<typename T, int S> vec<T, S> mix(vec<T, S> a, vec<T, S> b, float fac)
{
  return a * (1.0f - fac) + b * fac;
}

/* Using vec<bool, S> does not appear to work, splitting cases. */
#define SELECT \
  { \
    return select(a, b, mask); \
  }

template<typename T> vec<T, 4> mix(vec<T, 4> a, vec<T, 4> b, bool4 mask) SELECT;
template<typename T> vec<T, 3> mix(vec<T, 3> a, vec<T, 3> b, bool3 mask) SELECT;
template<typename T> vec<T, 2> mix(vec<T, 2> a, vec<T, 2> b, bool2 mask) SELECT;

#undef SELECT

#define in
#define out thread
#define inout thread
#define _in_sta
#define _in_end
#define _out_sta (&
#define _out_end )
#define _inout_sta (&
#define _inout_end )

#define shared threadgroup
#define _shared_sta (&
#define _shared_end )

/* Defines generated by glsl_preprocess that contains threadgroup variables related codegen.
 * See glsl_preprocess for more detail. */
#define MSL_SHARED_VARS_ARGS
#define MSL_SHARED_VARS_ASSIGN
#define MSL_SHARED_VARS_DECLARE
#define MSL_SHARED_VARS_PASS

/* Matrix reshaping functions. */
#define RESHAPE(mat_to, mat_from, ...) \
  mat_to to_##mat_to(mat_from m) \
  { \
    return mat_to(__VA_ARGS__); \
  }

/* clang-format off */
RESHAPE(float2x2, float3x3, m[0].xy, m[1].xy)
RESHAPE(float2x2, float4x4, m[0].xy, m[1].xy)
RESHAPE(float3x3, float4x4, m[0].xyz, m[1].xyz, m[2].xyz)
RESHAPE(float3x3, float2x2, m[0].x, m[0].y, 0, m[1].x, m[1].y, 0, 0, 0, 1)
RESHAPE(float4x4, float2x2, m[0].x, m[0].y, 0, 0, m[1].x, m[1].y, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1)
RESHAPE(float4x4, float3x3, m[0].x, m[0].y, m[0].z, 0, m[1].x, m[1].y, m[1].z, 0, m[2].x, m[2].y, m[2].z, 0, 0, 0, 0, 1)
/* clang-format on */
/* TODO(fclem): Remove. Use Transform instead. */
RESHAPE(float3x3, float3x4, m[0].xyz, m[1].xyz, m[2].xyz)
#undef RESHAPE

/* Matrix constructors functions for pyGPU shaders compatibility.
 * Unfortunately, we have to overload all of most common ones. */
/* clang-format off */
/* Scalar constructors. */
float2x2 __mat2x2(float x1, float x2,
                  float y1, float y2)
{
  return float2x2(x1, x2,
                  y1, y2);
}

float3x3 __mat3x3(float x1, float x2, float x3,
                  float y1, float y2, float y3,
                  float z1, float z2, float z3)
{
  return float3x3(x1, x2, x3,
                  y1, y2, y3,
                  z1, z2, z3);
}

float4x4 __mat4x4(float x1, float x2, float x3, float x4,
                  float y1, float y2, float y3, float y4,
                  float z1, float z2, float z3, float z4,
                  float w1, float w2, float w3, float w4)
{
  return float4x4(x1, x2, x3, x4,
                  y1, y2, y3, y4,
                  z1, z2, z3, z4,
                  w1, w2, w3, w4);
}

/* Diagonal constructors. */
float2x2 __mat2x2(float x) { return float2x2(x); }
float3x3 __mat3x3(float x) { return float3x3(x); }
float4x4 __mat4x4(float x) { return float4x4(x); }

/* Vector constructors. */
float2x2 __mat2x2(float2 x, float2 y) { return {x, y}; }
float3x3 __mat3x3(float3 x, float3 y, float3 z) { return {x, y, z}; }
float4x4 __mat4x4(float4 x, float4 y, float4 z, float4 w) { return {x, y, z, w}; }

/* Reshape constructors. */
float2x2 __mat2x2(float3x3 a) { return to_float2x2(a); }
float2x2 __mat2x2(float4x4 a) { return to_float2x2(a); }
float3x3 __mat3x3(float2x2 a) { return to_float3x3(a); }
float3x3 __mat3x3(float4x4 a) { return to_float3x3(a); }
float4x4 __mat4x4(float2x2 a) { return to_float4x4(a); }
float4x4 __mat4x4(float3x3 a) { return to_float4x4(a); }
/* clang-format on */

#define METAL_CONSTRUCTOR_1(class_name, t1, m1) \
  class_name() = default; \
  class_name(t1 m1##_) : m1(m1##_){};

#define METAL_CONSTRUCTOR_2(class_name, t1, m1, t2, m2) \
  class_name() = default; \
  class_name(t1 m1##_, t2 m2##_) : m1(m1##_), m2(m2##_){};

#define METAL_CONSTRUCTOR_3(class_name, t1, m1, t2, m2, t3, m3) \
  class_name() = default; \
  class_name(t1 m1##_, t2 m2##_, t3 m3##_) : m1(m1##_), m2(m2##_), m3(m3##_){};

#define METAL_CONSTRUCTOR_4(class_name, t1, m1, t2, m2, t3, m3, t4, m4) \
  class_name() = default; \
  class_name(t1 m1##_, t2 m2##_, t3 m3##_, t4 m4##_) \
      : m1(m1##_), m2(m2##_), m3(m3##_), m4(m4##_){};

#define METAL_CONSTRUCTOR_5(class_name, t1, m1, t2, m2, t3, m3, t4, m4, t5, m5) \
  class_name() = default; \
  class_name(t1 m1##_, t2 m2##_, t3 m3##_, t4 m4##_, t5 m5##_) \
      : m1(m1##_), m2(m2##_), m3(m3##_), m4(m4##_), m5(m5##_){};

#define METAL_CONSTRUCTOR_6(class_name, t1, m1, t2, m2, t3, m3, t4, m4, t5, m5, t6, m6) \
  class_name() = default; \
  class_name(t1 m1##_, t2 m2##_, t3 m3##_, t4 m4##_, t5 m5##_, t6 m6##_) \
      : m1(m1##_), m2(m2##_), m3(m3##_), m4(m4##_), m5(m5##_), m6(m6##_){};

#define METAL_CONSTRUCTOR_7(class_name, t1, m1, t2, m2, t3, m3, t4, m4, t5, m5, t6, m6, t7, m7) \
  class_name() = default; \
  class_name(t1 m1##_, t2 m2##_, t3 m3##_, t4 m4##_, t5 m5##_, t6 m6##_, t7 m7##_) \
      : m1(m1##_), m2(m2##_), m3(m3##_), m4(m4##_), m5(m5##_), m6(m6##_), m7(m7##_){};

#undef ENABLE_IF

/* Array syntax compatibility. */
/* clang-format off */
#define float_array(...) { __VA_ARGS__ }
#define float2_array(...) { __VA_ARGS__ }
#define float3_array(...) { __VA_ARGS__ }
#define float4_array(...) { __VA_ARGS__ }
#define int_array(...) { __VA_ARGS__ }
#define int2_array(...) { __VA_ARGS__ }
#define int3_array(...) { __VA_ARGS__ }
#define int4_array(...) { __VA_ARGS__ }
#define uint_array(...) { __VA_ARGS__ }
#define uint2_array(...) { __VA_ARGS__ }
#define uint3_array(...) { __VA_ARGS__ }
#define uint4_array(...) { __VA_ARGS__ }
#define bool_array(...) { __VA_ARGS__ }
#define bool2_array(...) { __VA_ARGS__ }
#define bool3_array(...) { __VA_ARGS__ }
#define bool4_array(...) { __VA_ARGS__ }
#define ARRAY_T(type)
#define ARRAY_V(...) {__VA_ARGS__}
/* clang-format on */

#define SHADER_LIBRARY_CREATE_INFO(a)
#define VERTEX_SHADER_CREATE_INFO(a)
#define FRAGMENT_SHADER_CREATE_INFO(a)
#define COMPUTE_SHADER_CREATE_INFO(a)

#define _enum_type(name) name
#define _enum_decl(name) enum name {
#define _enum_end \
  } \
  ;

/* Stage agnostic builtin function.
 * MSL allow mixing shader stages inside the same source file.
 * Leaving the calls untouched makes sure we catch invalid usage during CI testing. */
#define gpu_discard_fragment() discard
#define gpu_dfdx(x) dFdx(x)
#define gpu_dfdy(x) dFdy(x)
#define gpu_fwidth(x) fwidth(x)

/* Resource accessor. */
#define specialization_constant_get(create_info, _res) _res
#define shared_variable_get(create_info, _res) _res
#define push_constant_get(create_info, _res) _res
#define interface_get(create_info, _res) _res
#define attribute_get(create_info, _res) _res
#define buffer_get(create_info, _res) _res
#define sampler_get(create_info, _res) _res
#define image_get(create_info, _res) _res
